.logo-slider {
  padding: 120px 0 80px;
  background: var(--background-light);
}

.slider-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.slider-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem;
  color: var(--text-primary);
}

.slider-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.slider-btn {
  background: var(--background);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-secondary);
  transition: var(--transition);
  flex-shrink: 0;
  z-index: 10;
}

.slider-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.slider-content {
  flex: 1;
  overflow: hidden;
  border-radius: var(--border-radius);
}

.slides-container {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  display: flex;
  background: var(--background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.slide-image {
  flex: 1;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
}

.logo-placeholder-slide {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  box-shadow: var(--shadow);
}

.slide-info {
  flex: 1;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.slide-category {
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.slide-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.slide-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
}

.slider-dots {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--border-color);
  transition: var(--transition);
}

.dot.active {
  background: var(--primary-color);
  transform: scale(1.2);
}

.dot:hover {
  background: var(--primary-color);
}

@media (max-width: 768px) {
  .logo-slider {
    padding: 100px 0 60px;
  }
  
  .slider-container {
    padding: 0 1rem;
  }
  
  .slider-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .slider-wrapper {
    gap: 1rem;
  }
  
  .slider-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .slide {
    flex-direction: column;
  }
  
  .slide-image {
    min-height: 250px;
  }
  
  .logo-placeholder-slide {
    width: 150px;
    height: 150px;
  }
  
  .slide-info {
    padding: 2rem;
  }
  
  .slide-title {
    font-size: 1.5rem;
  }
  
  .slide-description {
    font-size: 1rem;
  }
}
