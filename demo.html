<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adam's Designs - Logo Designer Portfolio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --accent-color: #f59e0b;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --background: #ffffff;
            --background-light: #f8fafc;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            background-color: var(--background);
        }

        html {
            scroll-behavior: smooth;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80px;
        }

        .logo-placeholder {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: var(--border-radius);
            color: white;
            min-width: 120px;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
            letter-spacing: -0.025em;
        }

        .logo-subtext {
            font-size: 0.75rem;
            font-weight: 500;
            letter-spacing: 0.1em;
            opacity: 0.9;
            margin-top: -2px;
        }

        .nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-secondary);
            text-decoration: none;
            transition: var(--transition);
            padding: 0.5rem 0;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        /* Logo Slider Styles */
        .logo-slider {
            padding: 120px 0 80px;
            background: var(--background-light);
        }

        .slider-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .slider-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--text-primary);
        }

        .slide {
            display: flex;
            background: var(--background);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .slide-image {
            flex: 1;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
        }

        .logo-placeholder-slide {
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: var(--shadow);
        }

        .slide-info {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .slide-category {
            color: var(--primary-color);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .slide-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .slide-description {
            color: var(--text-secondary);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        /* About Me Styles */
        .about-me {
            padding: 80px 0;
            background: var(--background);
        }

        .about-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .about-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 2rem;
            line-height: 1.2;
        }

        .about-description {
            color: var(--text-secondary);
            font-size: 1.1rem;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .about-stats {
            display: flex;
            gap: 2rem;
            margin: 2rem 0;
            padding: 2rem 0;
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
        }

        .stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.5rem;
            font-weight: 500;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .skill-tag {
            background: var(--background-light);
            color: var(--text-secondary);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        .contact-btn {
            background: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            margin-top: 2rem;
            border: none;
            cursor: pointer;
            transition: var(--transition);
        }

        .contact-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .profile-placeholder {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, var(--accent-color), #f97316);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: 700;
            box-shadow: var(--shadow-lg);
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .header-container {
                padding: 0 1rem;
            }
            
            .nav {
                gap: 1rem;
            }
            
            .slider-title, .about-title {
                font-size: 2rem;
            }
            
            .slide {
                flex-direction: column;
            }
            
            .slide-image {
                min-height: 250px;
            }
            
            .slide-info {
                padding: 2rem;
            }
            
            .about-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }
            
            .about-stats {
                justify-content: center;
                gap: 1.5rem;
            }
            
            .profile-placeholder {
                width: 250px;
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo-section">
                <div class="logo-placeholder">
                    <span class="logo-text">Adam's</span>
                    <span class="logo-subtext">DESIGNS</span>
                </div>
            </div>
            <nav class="nav">
                <a href="#portfolio" class="nav-link">Portfolio</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#contact" class="nav-link">Contact</a>
            </nav>
        </div>
    </header>

    <!-- Logo Slider Section -->
    <section id="portfolio" class="logo-slider">
        <div class="slider-container">
            <h2 class="slider-title">Featured Logo Designs</h2>
            
            <div class="slide">
                <div class="slide-image">
                    <div class="logo-placeholder-slide">
                        <span>Logo 1</span>
                    </div>
                </div>
                <div class="slide-info">
                    <span class="slide-category">Technology</span>
                    <h3 class="slide-title">Tech Startup Logo</h3>
                    <p class="slide-description">Modern minimalist design for a technology company focused on innovation and user experience.</p>
                </div>
            </div>

            <div class="slide">
                <div class="slide-image">
                    <div class="logo-placeholder-slide">
                        <span>Logo 2</span>
                    </div>
                </div>
                <div class="slide-info">
                    <span class="slide-category">Food & Beverage</span>
                    <h3 class="slide-title">Restaurant Brand</h3>
                    <p class="slide-description">Elegant logo design for fine dining establishment with sophisticated typography and color palette.</p>
                </div>
            </div>

            <div class="slide">
                <div class="slide-image">
                    <div class="logo-placeholder-slide">
                        <span>Logo 3</span>
                    </div>
                </div>
                <div class="slide-info">
                    <span class="slide-category">Health & Fitness</span>
                    <h3 class="slide-title">Fitness Brand</h3>
                    <p class="slide-description">Dynamic logo for a fitness and wellness company emphasizing strength and vitality.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- About Me Section -->
    <section id="about" class="about-me">
        <div class="about-container">
            <div class="about-content">
                <div class="about-text">
                    <h2 class="about-title">About Adam</h2>
                    <p class="about-description">
                        Welcome to Adam's Designs! I'm a passionate logo designer with over 5 years of experience 
                        creating memorable brand identities that help businesses stand out in today's competitive market.
                    </p>
                    <p class="about-description">
                        My design philosophy centers around simplicity, versatility, and timeless appeal. I believe 
                        that a great logo should be instantly recognizable, work across all mediums, and tell your 
                        brand's story at a glance.
                    </p>
                    <div class="about-stats">
                        <div class="stat">
                            <span class="stat-number">150+</span>
                            <span class="stat-label">Logos Created</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">100+</span>
                            <span class="stat-label">Happy Clients</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">5+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                    </div>
                    <div class="skills-list">
                        <span class="skill-tag">Logo Design</span>
                        <span class="skill-tag">Brand Identity</span>
                        <span class="skill-tag">Typography</span>
                        <span class="skill-tag">Color Theory</span>
                        <span class="skill-tag">Vector Graphics</span>
                        <span class="skill-tag">Brand Guidelines</span>
                    </div>
                    <button class="contact-btn">Get In Touch</button>
                </div>
                <div class="about-image">
                    <div class="profile-placeholder">
                        <span>Adam</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
