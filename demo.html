<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adam's Designs - Logo Designer Portfolio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --accent-color: #f59e0b;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --background: #ffffff;
            --background-light: #f8fafc;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            background-color: var(--background);
        }

        html {
            scroll-behavior: smooth;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80px;
        }

        .logo-section img {
            height: 50px;
            width: auto;
            max-width: 200px;
        }

        .nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-secondary);
            text-decoration: none;
            transition: var(--transition);
            padding: 0.5rem 0;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        /* Logo Slider Styles */
        .logo-slider {
            padding: 120px 0 80px;
            background: var(--background-light);
        }

        .slider-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .slider-title {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--text-primary);
        }

        .slider-wrapper {
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            background: var(--background);
        }

        .slider-track {
            display: flex;
            transition: transform 0.5s ease-in-out;
            width: 500%; /* 5 slides * 100% */
        }

        .slide {
            min-width: 20%; /* 100% / 5 slides */
            display: flex;
            background: var(--background);
        }

        .slide-image {
            flex: 1;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            padding: 2rem;
        }

        .logo-image {
            max-width: 300px;
            max-height: 300px;
            width: auto;
            height: auto;
            object-fit: contain;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }

        .slide-info {
            flex: 1;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .slide-category {
            color: var(--primary-color);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }

        .slide-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .slide-description {
            color: var(--text-secondary);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .slider-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--border-color);
            cursor: pointer;
            transition: var(--transition);
        }

        .dot.active {
            background: var(--primary-color);
            transform: scale(1.2);
        }

        .dot:hover {
            background: var(--primary-color);
        }

        /* About Me Styles */
        .about-me {
            padding: 80px 0;
            background: var(--background);
        }

        .about-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .about-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .about-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 2rem;
            line-height: 1.2;
        }

        .about-description {
            color: var(--text-secondary);
            font-size: 1.1rem;
            line-height: 1.7;
            margin-bottom: 1.5rem;
        }

        .about-stats {
            display: flex;
            gap: 2rem;
            margin: 2rem 0;
            padding: 2rem 0;
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
        }

        .stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 0.5rem;
            font-weight: 500;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .skill-tag {
            background: var(--background-light);
            color: var(--text-secondary);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            border: 1px solid var(--border-color);
        }

        .contact-btn {
            background: var(--primary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            margin-top: 2rem;
            border: none;
            cursor: pointer;
            transition: var(--transition);
        }

        .contact-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .profile-placeholder {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, var(--accent-color), #f97316);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: 700;
            box-shadow: var(--shadow-lg);
            margin: 0 auto;
        }

        /* Contact Section Styles */
        .contact-section {
            background: var(--background-light);
            padding: 60px 0;
            border-top: 1px solid var(--border-color);
        }

        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .contact-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .contact-subtitle {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 3rem;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .contact-item {
            background: var(--background);
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .contact-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .contact-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .contact-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .contact-value {
            color: var(--text-secondary);
        }

        .contact-value a {
            color: var(--primary-color);
            text-decoration: none;
            transition: var(--transition);
        }

        .contact-value a:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .social-link {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .social-link:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        @media (max-width: 768px) {
            .header-container {
                padding: 0 1rem;
            }

            .nav {
                gap: 1rem;
            }

            .slider-title, .about-title {
                font-size: 2rem;
            }

            .slide {
                flex-direction: column;
            }

            .slide-image {
                min-height: 250px;
            }

            .slide-info {
                padding: 2rem;
            }

            .logo-image {
                max-width: 200px;
                max-height: 200px;
            }

            .slide-image {
                padding: 1rem;
            }

            .slide-title {
                font-size: 1.5rem;
            }

            .slide-description {
                font-size: 1rem;
            }

            .about-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
            }

            .about-stats {
                justify-content: center;
                gap: 1.5rem;
            }

            .profile-placeholder {
                width: 250px;
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-container">
            <div class="logo-section">
                <img src="Adam's Designs.png" alt="Adam's Designs Logo">
            </div>
            <nav class="nav">
                <a href="#portfolio" class="nav-link">Portfolio</a>
                <a href="#about" class="nav-link">About</a>
                <a href="#contact" class="nav-link">Contact</a>
            </nav>
        </div>
    </header>

    <!-- Logo Slider Section -->
    <section id="portfolio" class="logo-slider">
        <div class="slider-container">
            <h2 class="slider-title">My Logo Designs</h2>

            <div class="slider-wrapper">
                <div class="slider-track" id="sliderTrack">
                    <div class="slide">
                        <div class="slide-image">
                            <img src="DataDash.png" alt="DataDash Logo" class="logo-image">
                        </div>
                        <div class="slide-info">
                            <span class="slide-category">Network Services</span>
                            <h3 class="slide-title">DataDash</h3>
                            <p class="slide-description">Professional logo design for a network services company, emphasizing speed, connectivity, and reliability in data management.</p>
                        </div>
                    </div>

                    <div class="slide">
                        <div class="slide-image">
                            <img src="TS.png" alt="TS Construction Logo" class="logo-image">
                        </div>
                        <div class="slide-info">
                            <span class="slide-category">Construction</span>
                            <h3 class="slide-title">TS Construction</h3>
                            <p class="slide-description">Strong and dependable logo design for a construction company, conveying trust, stability, and professional craftsmanship.</p>
                        </div>
                    </div>

                    <div class="slide">
                        <div class="slide-image">
                            <img src="GlowPrint.png" alt="GlowPrint Logo" class="logo-image">
                        </div>
                        <div class="slide-info">
                            <span class="slide-category">Cyber Security</span>
                            <h3 class="slide-title">GlowPrint</h3>
                            <p class="slide-description">Modern and secure logo design for a cyber security company, focusing on protection, innovation, and digital trust.</p>
                        </div>
                    </div>

                    <div class="slide">
                        <div class="slide-image">
                            <img src="CentreX.png" alt="CentreX Solution Logo" class="logo-image">
                        </div>
                        <div class="slide-info">
                            <span class="slide-category">Technology</span>
                            <h3 class="slide-title">CentreX Solution</h3>
                            <p class="slide-description">Clean and professional logo design for a tech company, representing innovation, solutions, and forward-thinking technology.</p>
                        </div>
                    </div>

                    <div class="slide">
                        <div class="slide-image">
                            <img src="Adam's Designs.png" alt="Adam's Designs Logo" class="logo-image">
                        </div>
                        <div class="slide-info">
                            <span class="slide-category">Personal Brand</span>
                            <h3 class="slide-title">Adam's Designs</h3>
                            <p class="slide-description">My personal brand logo, representing creativity, professionalism, and passion for logo design and brand identity.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="slider-dots">
                <span class="dot active" onclick="currentSlide(1)"></span>
                <span class="dot" onclick="currentSlide(2)"></span>
                <span class="dot" onclick="currentSlide(3)"></span>
                <span class="dot" onclick="currentSlide(4)"></span>
                <span class="dot" onclick="currentSlide(5)"></span>
            </div>
        </div>
    </section>

    <!-- About Me Section -->
    <section id="about" class="about-me">
        <div class="about-container">
            <div class="about-content">
                <div class="about-text">
                    <h2 class="about-title">About Adam</h2>
                    <p class="about-description">
                        Welcome to Adam's Designs! I'm an aspiring logo designer passionate about creating
                        memorable brand identities. I'm just starting my journey but excited to help businesses
                        stand out with creative and thoughtful design.
                    </p>
                    <p class="about-description">
                        My design philosophy centers around simplicity, creativity, and understanding each client's
                        unique story. I believe that even as I'm learning, every logo should be meaningful and
                        reflect the brand's personality.
                    </p>
                    <div class="about-stats">
                        <div class="stat">
                            <span class="stat-number">10</span>
                            <span class="stat-label">Logos Created</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">0</span>
                            <span class="stat-label">Clients (Yet!)</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Passion</span>
                        </div>
                    </div>
                    <div class="skills-list">
                        <span class="skill-tag">Logo Design</span>
                        <span class="skill-tag">Brand Identity</span>
                        <span class="skill-tag">Typography</span>
                        <span class="skill-tag">Color Theory</span>
                        <span class="skill-tag">Vector Graphics</span>
                        <span class="skill-tag">Brand Guidelines</span>
                    </div>
                    <button class="contact-btn">Get In Touch</button>
                </div>
                <div class="about-image">
                    <div class="profile-placeholder">
                        <span>Adam</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="contact-container">
            <h2 class="contact-title">Let's Work Together!</h2>
            <p class="contact-subtitle">Ready to create something amazing? Get in touch and let's bring your brand to life!</p>

            <div class="contact-info">
                <div class="contact-item">
                    <div class="contact-icon">📞</div>
                    <div class="contact-label">Phone</div>
                    <div class="contact-value">+****************</div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon">✉️</div>
                    <div class="contact-label">Email</div>
                    <div class="contact-value">
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon">💼</div>
                    <div class="contact-label">Availability</div>
                    <div class="contact-value">Open for new projects</div>
                </div>
            </div>

            <div class="social-links">
                <a href="https://kwork.com/user/adamdesigns" class="social-link" target="_blank">Kwork Profile</a>
                <a href="https://upwork.com/freelancers/adamdesigns" class="social-link" target="_blank">Upwork Profile</a>
                <a href="https://fiverr.com/adamdesigns" class="social-link" target="_blank">Fiverr Profile</a>
                <a href="https://behance.net/adamdesigns" class="social-link" target="_blank">Behance Portfolio</a>
                <a href="https://dribbble.com/adamdesigns" class="social-link" target="_blank">Dribbble</a>
            </div>
        </div>
    </section>

    <script>
        let slideIndex = 1;
        const totalSlides = 5;

        function showSlide(n) {
            const track = document.getElementById('sliderTrack');
            const dots = document.querySelectorAll('.dot');

            if (n > totalSlides) { slideIndex = 1; }
            if (n < 1) { slideIndex = totalSlides; }

            // Move the slider track
            const translateX = -((slideIndex - 1) * 20); // 20% per slide
            track.style.transform = `translateX(${translateX}%)`;

            // Update dots
            dots.forEach(dot => dot.classList.remove('active'));
            dots[slideIndex - 1].classList.add('active');
        }

        function currentSlide(n) {
            slideIndex = n;
            showSlide(slideIndex);
        }

        function nextSlide() {
            slideIndex++;
            showSlide(slideIndex);
        }

        // Auto-advance slides every 4 seconds
        setInterval(nextSlide, 4000);

        // Initialize
        showSlide(slideIndex);
    </script>
</body>
</html>
