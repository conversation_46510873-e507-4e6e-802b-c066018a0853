.header {
  background: var(--background);
  box-shadow: var(--shadow);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--border-radius);
  color: white;
  min-width: 120px;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  letter-spacing: -0.025em;
}

.logo-subtext {
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.1em;
  opacity: 0.9;
  margin-top: -2px;
}

.nav {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  font-weight: 500;
  color: var(--text-secondary);
  transition: var(--transition);
  padding: 0.5rem 0;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
}

@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
  }
  
  .nav {
    gap: 1rem;
  }
  
  .nav-link {
    font-size: 0.9rem;
  }
  
  .logo-placeholder {
    min-width: 100px;
    padding: 0.4rem 0.8rem;
  }
  
  .logo-text {
    font-size: 1.2rem;
  }
  
  .logo-subtext {
    font-size: 0.7rem;
  }
}
