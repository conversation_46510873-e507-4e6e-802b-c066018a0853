.about-me {
  padding: 80px 0;
  background: var(--background);
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
  line-height: 1.2;
}

.about-description {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.about-stats {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
  padding: 2rem 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-top: 0.5rem;
  font-weight: 500;
}

.about-skills {
  margin: 2rem 0;
}

.skills-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: var(--background-light);
  color: var(--text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.skill-tag:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.contact-btn {
  background: var(--primary-color);
  color: white;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  margin-top: 2rem;
  transition: var(--transition);
}

.contact-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.about-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-placeholder {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, var(--accent-color), #f97316);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  box-shadow: var(--shadow-lg);
  position: relative;
}

.profile-placeholder::before {
  content: '';
  position: absolute;
  inset: -10px;
  background: linear-gradient(135deg, var(--accent-color), #f97316);
  border-radius: 50%;
  z-index: -1;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.1;
  }
}

@media (max-width: 768px) {
  .about-me {
    padding: 60px 0;
  }
  
  .about-container {
    padding: 0 1rem;
  }
  
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
  
  .about-title {
    font-size: 2rem;
  }
  
  .about-description {
    font-size: 1rem;
  }
  
  .about-stats {
    justify-content: center;
    gap: 1.5rem;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .profile-placeholder {
    width: 250px;
    height: 250px;
    font-size: 1.5rem;
  }
  
  .skills-list {
    justify-content: center;
  }
}
