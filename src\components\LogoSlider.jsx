import React, { useState, useEffect } from 'react'
import './LogoSlider.css'

const LogoSlider = () => {
  const [currentSlide, setCurrentSlide] = useState(0)

  // Sample logo data - replace with actual logo images
  const logos = [
    {
      id: 1,
      title: "Tech Startup Logo",
      description: "Modern minimalist design for a technology company",
      image: "/api/placeholder/400/300",
      category: "Technology"
    },
    {
      id: 2,
      title: "Restaurant Brand",
      description: "Elegant logo design for fine dining establishment",
      image: "/api/placeholder/400/300",
      category: "Food & Beverage"
    },
    {
      id: 3,
      title: "Fitness Brand",
      description: "Dynamic logo for a fitness and wellness company",
      image: "/api/placeholder/400/300",
      category: "Health & Fitness"
    },
    {
      id: 4,
      title: "Creative Agency",
      description: "Bold and creative logo for a design agency",
      image: "/api/placeholder/400/300",
      category: "Creative"
    },
    {
      id: 5,
      title: "E-commerce Brand",
      description: "Clean and trustworthy logo for online retail",
      image: "/api/placeholder/400/300",
      category: "E-commerce"
    }
  ]

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % logos.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + logos.length) % logos.length)
  }

  const goToSlide = (index) => {
    setCurrentSlide(index)
  }

  // Auto-advance slides
  useEffect(() => {
    const timer = setInterval(nextSlide, 5000)
    return () => clearInterval(timer)
  }, [])

  return (
    <section id="portfolio" className="logo-slider">
      <div className="slider-container">
        <h2 className="slider-title">Featured Logo Designs</h2>
        
        <div className="slider-wrapper">
          <button className="slider-btn prev-btn" onClick={prevSlide}>
            &#8249;
          </button>
          
          <div className="slider-content">
            <div 
              className="slides-container"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {logos.map((logo, index) => (
                <div key={logo.id} className="slide">
                  <div className="slide-image">
                    <div className="logo-placeholder-slide">
                      <span className="placeholder-text">Logo {logo.id}</span>
                    </div>
                  </div>
                  <div className="slide-info">
                    <span className="slide-category">{logo.category}</span>
                    <h3 className="slide-title">{logo.title}</h3>
                    <p className="slide-description">{logo.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <button className="slider-btn next-btn" onClick={nextSlide}>
            &#8250;
          </button>
        </div>
        
        <div className="slider-dots">
          {logos.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentSlide ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
            />
          ))}
        </div>
      </div>
    </section>
  )
}

export default LogoSlider
